[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "endstone-warp-system"
version = "1.0.0"
description = "A comprehensive warp system plugin for Endstone/Minecraft Bedrock"
readme = "README.md"
requires-python = ">=3.9"
license = { text = "Apache-2.0" }
authors = [
    { name = "Your Name", email = "<EMAIL>" }
]
keywords = ["endstone", "minecraft", "bedrock", "plugin", "warp", "teleport"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    "endstone>=0.10.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/endstone-warp-system"
Documentation = "https://github.com/yourusername/endstone-warp-system/blob/main/README.md"
Repository = "https://github.com/yourusername/endstone-warp-system"
Issues = "https://github.com/yourusername/endstone-warp-system/issues"

[project.entry-points.endstone]
warp-system = "endstone_warp_system:WarpSystemPlugin"

[tool.setuptools.packages.find]
where = ["src"]
include = ["endstone_warp_system*"]