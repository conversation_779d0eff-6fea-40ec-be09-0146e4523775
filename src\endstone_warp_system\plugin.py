"""
Endstone Warp System Plugin - Compatible with Endstone 0.10.6+
A complete warp management system for Minecraft Bedrock Edition

Author: Your Name
Version: 1.0.0
API Version: 0.5
"""

from endstone.plugin import Plugin
from endstone.event import event_handler, PlayerInteractEvent, PlayerQuitEvent
from endstone import Player, ColorFormat
from endstone.form import ActionForm, ModalForm, Dropdown, TextInput
from endstone.level import Location
import json
import time
from pathlib import Path
from typing import Dict, List, Any


class WarpSystemPlugin(Plugin):
    # Plugin metadata - NO YAML FILE NEEDED
    api_version = "0.5"

    # Plugin description
    description = "A comprehensive warp system with custom player warps and admin tools"

    def __init__(self):
        super().__init__()
        self.warp_db: Dict[str, Any] = {}
        self.cooldown_db: Dict[str, Any] = {}
        self.fixed_warps_db: Dict[str, Any] = {}
        self.data_folder_path: Path = None
        self.interaction_cooldown: Dict[str, float] = {}  # Track interaction cooldowns
        self.players_with_forms: set = set()  # Track players who have forms open

        # Item to Minecraft native tag mapping
        self.warp_item_mapping = {
            "ninjos:rope1": "WARP1",
            "ninjos:rope2": "WARP2",
            "ninjos:rope3": "WARP3"
        }

        # Dimension display names
        self.dimension_names = {
            "minecraft:overworld": "Overworld",
            "minecraft:nether": "Nether",
            "minecraft:the_end": "End"
        }

    def on_load(self) -> None:
        """Called when the plugin is loaded"""
        self.logger.info(f"{ColorFormat.YELLOW}Warp System Plugin loading...")

    def on_enable(self) -> None:
        """Called when the plugin is enabled"""
        self.data_folder_path = Path(self.data_folder)
        self.data_folder_path.mkdir(parents=True, exist_ok=True)

        # Load databases
        self.load_database()

        # Register event listeners
        self.register_events(self)

        self.logger.info(f"{ColorFormat.GREEN}Warp System Plugin v1.0.0 enabled!")
        self.logger.info(f"{ColorFormat.GREEN}Data folder: {self.data_folder_path}")

    def on_disable(self) -> None:
        """Called when the plugin is disabled"""
        self.save_database()
        self.logger.info(f"{ColorFormat.RED}Warp System Plugin disabled!")

    def load_database(self) -> None:
        """Load warp and cooldown databases from JSON files"""
        warp_file = self.data_folder_path / "warps.json"
        cooldown_file = self.data_folder_path / "cooldowns.json"
        fixed_warps_file = self.data_folder_path / "fixed_warps.json"

        try:
            if warp_file.exists():
                with open(warp_file, 'r') as f:
                    self.warp_db = json.load(f)
            else:
                self.warp_db = {}

            if cooldown_file.exists():
                with open(cooldown_file, 'r') as f:
                    self.cooldown_db = json.load(f)
            else:
                self.cooldown_db = {}

            # Load fixed warps with defaults
            if fixed_warps_file.exists():
                with open(fixed_warps_file, 'r') as f:
                    self.fixed_warps_db = json.load(f)
            else:
                # Default fixed warps
                self.fixed_warps_db = {
                    "spawn": {
                        "name": "Spawn",
                        "coords": [438, 97, 757],
                        "dimension": "minecraft:overworld"
                    },
                    "shopping": {
                        "name": "Shopping District",
                        "coords": [411, 64, 184],
                        "dimension": "minecraft:overworld"
                    }
                }
                self.save_database()

            self.logger.info(f"Loaded {len(self.warp_db)} player warp records")
            self.logger.info(f"Loaded {len(self.fixed_warps_db)} fixed warps")
        except Exception as e:
            self.logger.error(f"Error loading databases: {e}")
            self.warp_db = {}
            self.cooldown_db = {}
            self.fixed_warps_db = {}

    def save_database(self) -> None:
        """Save warp and cooldown databases to JSON files"""
        try:
            warp_file = self.data_folder_path / "warps.json"
            cooldown_file = self.data_folder_path / "cooldowns.json"
            fixed_warps_file = self.data_folder_path / "fixed_warps.json"

            with open(warp_file, 'w') as f:
                json.dump(self.warp_db, f, indent=2)

            with open(cooldown_file, 'w') as f:
                json.dump(self.cooldown_db, f, indent=2)

            with open(fixed_warps_file, 'w') as f:
                json.dump(self.fixed_warps_db, f, indent=2)

        except Exception as e:
            self.logger.error(f"Error saving databases: {e}")

    @event_handler
    def on_player_interact(self, event: PlayerInteractEvent) -> None:
        """Handle player item interaction events"""
        player = event.player
        item = event.item

        if not player or not item:
            return

        # Check if this is a right-click action
        if event.action != PlayerInteractEvent.Action.RIGHT_CLICK_AIR and \
                event.action != PlayerInteractEvent.Action.RIGHT_CLICK_BLOCK:
            return

        # Get the item type ID (string identifier)
        item_type_id = item.type.id
        required_tag = self.warp_item_mapping.get(item_type_id)

        if not required_tag:
            return

        # Check interaction cooldown to prevent multiple forms opening
        player_name = player.name
        current_time = time.time()
        last_interaction = self.interaction_cooldown.get(player_name, 0)

        # 1 second cooldown between interactions
        if current_time - last_interaction < 1.0:
            return

        self.interaction_cooldown[player_name] = current_time

        # Check if player has the required Minecraft native scoreboard tag OR Admin tag
        player_tags = player.scoreboard_tags
        if required_tag not in player_tags and "Admin" not in player_tags:
            player.send_message(f"{ColorFormat.RED}You need the Required Donation Privileges to use this item!")
            return

        # Show main menu
        self.show_main_menu(player)

    @event_handler
    def on_player_quit(self, event: PlayerQuitEvent) -> None:
        """Handle player quit events to clean up form tracking"""
        player = event.player
        if player and player.name:
            # Clean up form tracking when player disconnects
            self.players_with_forms.discard(player.name)
            # Clean up interaction cooldown tracking
            self.interaction_cooldown.pop(player.name, None)

    def safe_send_form(self, player: Player, form) -> bool:
        """Safely send a form to a player, preventing multiple forms"""
        player_name = player.name

        # Check if player already has a form open
        if player_name in self.players_with_forms:
            return False

        # Mark player as having a form open
        self.players_with_forms.add(player_name)

        # Add close handler to remove player from tracking
        original_on_close = getattr(form, 'on_close', None)

        def safe_on_close(p):
            # Remove player from tracking when form closes
            self.players_with_forms.discard(p.name)
            # Call original close handler if it exists
            if original_on_close:
                original_on_close(p)

        form.on_close = safe_on_close

        # Send the form
        player.send_form(form)
        return True

    def has_tag(self, player: Player, tag: str) -> bool:
        """Check if player has a specific Minecraft scoreboard tag"""
        return tag in player.scoreboard_tags

    def get_player_warp_data(self, player: Player) -> Dict[str, Any]:
        """Get player's warp data and maximum custom warp slots"""
        player_name = player.name
        player_data = self.warp_db.get(player_name, {"customWarps": []})

        # Get player's scoreboard tags
        player_tags = player.scoreboard_tags

        # Determine max custom warps based on tags
        max_custom = 0
        if "WARP1" in player_tags:
            max_custom = 3
        if "WARP2" in player_tags:
            max_custom = 5
        if "WARP3" in player_tags:
            max_custom = 8
        if "Admin" in player_tags or "Owner" in player_tags:
            max_custom = float('inf')

        return {
            "playerName": player_name,
            "customWarps": player_data.get("customWarps", []),
            "maxCustom": max_custom
        }

    def show_main_menu(self, player: Player) -> None:
        """Display the main warp menu to the player"""
        try:
            warp_data = self.get_player_warp_data(player)
            custom_warps = warp_data["customWarps"]
            max_custom = warp_data["maxCustom"]

            form = ActionForm(title="Warp System")

            # Store warp references for button handling
            visible_warps = custom_warps[:len(custom_warps) if max_custom == float('inf') else int(max_custom)]

            # Add fixed warps dynamically from database
            for warp_key, warp_data in self.fixed_warps_db.items():
                display_dim = self.dimension_names.get(warp_data["dimension"], warp_data["dimension"])
                coords_str = " ".join(str(c) for c in warp_data["coords"])
                button_text = f"{warp_data['name']}\n§8{coords_str} - {display_dim}"
                form.add_button(button_text, "textures/items/ender_pearl",
                                lambda p, w=warp_data: self.teleport_to_location(p, w["coords"][0], w["coords"][1],
                                                                                 w["coords"][2], w["dimension"]))

            # Add custom warps
            for i, warp in enumerate(visible_warps):
                display_dim = self.dimension_names.get(warp["dimension"], warp["dimension"])
                coords_str = " ".join(str(c) for c in warp["coords"])
                button_text = f"{warp['name']}\n§8{coords_str} - {display_dim}"
                # Create closure to capture current warp data
                form.add_button(button_text, "textures/ui/World",
                                lambda p, w=warp, idx=i: self.show_warp_options(p, w, idx))

            # Add "New Warp" button if slots available
            if len(custom_warps) < max_custom:
                form.add_button("Add New Warp (+)\n§8Create a new warp point", "textures/ui/trade_icon",
                                lambda p: self.show_add_warp_form(p))

            # Admin buttons - check for Admin or Owner tags
            if self.has_tag(player, "Owner") or self.has_tag(player, "Admin"):
                form.add_button("Manage Fixed Warps\n§8Edit spawn & server warps", "textures/ui/op",
                                lambda p: self.show_manage_fixed_warps_menu(p))
                form.add_button("Manage Player Cooldowns\n§8Reset edit timers", "textures/ui/settings_glyph_color_2x",
                                lambda p: self.show_manage_cooldown_form(p))
                form.add_button("View All Players' Warps\n§8Browse all warps", "textures/ui/FriendsDiversity",
                                lambda p: self.show_all_players_warps(p))

            if not self.safe_send_form(player, form):
                player.send_message(f"{ColorFormat.YELLOW}Please close your current form first.")

        except Exception as e:
            self.logger.error(f"Error in show_main_menu: {e}")
            player.send_message(f"{ColorFormat.RED}Error opening warp menu. Please try again.")

    def show_warp_options(self, player: Player, warp: Dict, index: int) -> None:
        """Show options for a specific warp"""
        try:
            form = ActionForm(title=warp["name"])

            # Teleport button
            form.add_button("Teleport\n§8Go to this warp", "textures/items/ender_pearl",
                            lambda p: self.handle_teleport_to_warp(p, warp))

            # Edit button
            form.add_button("Edit\n§8Modify warp details", "textures/ui/editIcon",
                            lambda p: self.handle_edit_warp(p, warp, index))

            # Delete button
            form.add_button("Delete\n§8Remove this warp", "textures/ui/trash",
                            lambda p: self.handle_delete_warp(p, warp, index))

            # Back button
            form.add_button("Back\n§8Return to main menu", "textures/ui/back_button",
                            lambda p: self.show_main_menu(p))

            if not self.safe_send_form(player, form):
                player.send_message(f"{ColorFormat.YELLOW}Please close your current form first.")

        except Exception as e:
            self.logger.error(f"Error in show_warp_options: {e}")
            player.send_message(f"{ColorFormat.RED}Error opening warp options.")

    def handle_teleport_to_warp(self, player: Player, warp: Dict) -> None:
        """Teleport player to a warp"""
        coords = warp["coords"]
        self.teleport_to_location(player, coords[0], coords[1], coords[2], warp["dimension"])

    def handle_edit_warp(self, player: Player, warp: Dict, index: int) -> None:
        """Handle edit warp with cooldown check"""
        if not self.check_cooldown(player):
            return
        self.show_edit_warp_form(player, warp, index)

    def handle_delete_warp(self, player: Player, warp: Dict, index: int) -> None:
        """Handle delete warp with cooldown check"""
        if not self.check_cooldown(player):
            return

        player_name = player.name
        warp_data = self.get_player_warp_data(player)
        warp_data["customWarps"].pop(index)
        self.warp_db[player_name] = {"customWarps": warp_data["customWarps"]}

        # Only apply cooldown if not Admin
        if not self.has_tag(player, "Admin"):
            self.cooldown_db[player_name] = {"lastEdit": int(time.time() * 1000)}

        self.save_database()
        player.send_message(f"{ColorFormat.RED}Deleted warp \"{warp['name']}\"")
        self.show_main_menu(player)

    def check_cooldown(self, player: Player) -> bool:
        """Check if player is on cooldown for editing/deleting warps"""
        # Admins bypass cooldown
        if self.has_tag(player, "Admin"):
            return True

        player_name = player.name
        last_edit = self.cooldown_db.get(player_name, {}).get("lastEdit", 0)
        cooldown_remaining = (6 * 60 * 60 * 1000) - (int(time.time() * 1000) - last_edit)

        if cooldown_remaining > 0:
            minutes = cooldown_remaining // (60 * 1000)
            player.send_message(
                f"{ColorFormat.RED}You can only edit/delete warps every 6 hours. Please wait {minutes} more minutes.")
            return False
        return True

    def show_add_warp_form(self, player: Player) -> None:
        """Show form to add a new warp"""
        try:
            form = ModalForm(title="Add New Warp")

            form.add_control(TextInput(
                label="Warp Name (max 15 chars)",
                placeholder="Enter name..."
            ))
            form.add_control(Dropdown(
                label="Dimension",
                options=["Overworld", "Nether", "End"],
                default_index=0
            ))
            form.add_control(TextInput(
                label="Coordinates (optional)",
                placeholder="x y z - Leave blank for current position"
            ))

            def on_submit(p: Player, data: str):
                try:
                    # Parse JSON response from modal form
                    response = json.loads(data)

                    name = response[0][:15].strip()
                    dimension_index = response[1]
                    coords_str = response[2]

                    if not name:
                        p.send_message(f"{ColorFormat.RED}Please enter a name for your warp!")
                        return

                    warp_data = self.get_player_warp_data(p)
                    dimension_map = ["overworld", "nether", "the_end"]

                    if coords_str.strip():
                        # Parse custom coordinates
                        coords = [int(x) for x in coords_str.split() if x.strip().lstrip('-').isdigit()]
                        if len(coords) != 3:
                            p.send_message(f"{ColorFormat.RED}Invalid coordinates! Use whole numbers like '100 64 200'")
                            return
                        dimension = dimension_map[dimension_index]
                    else:
                        # Use current position
                        loc = p.location
                        coords = [int(loc.x), int(loc.y), int(loc.z)]
                        # Get dimension name from player's current dimension
                        dim_name = p.dimension.name
                        dimension = dim_name.split(":")[-1] if ":" in dim_name else dim_name

                    warp_data["customWarps"].append({
                        "name": name,
                        "coords": coords,
                        "dimension": dimension
                    })

                    self.warp_db[p.name] = {"customWarps": warp_data["customWarps"]}
                    self.save_database()

                    display_dim = self.dimension_names.get(dimension, dimension)
                    coords_str = " ".join(str(c) for c in coords)
                    p.send_message(f"{ColorFormat.GREEN}Added new warp: {name} at {coords_str} in {display_dim}")
                    self.show_main_menu(p)
                except Exception as e:
                    self.logger.error(f"Error in add warp form submission: {e}")
                    p.send_message(f"{ColorFormat.RED}Error adding warp: {str(e)}")

            form.on_submit = on_submit

            if not self.safe_send_form(player, form):
                player.send_message(f"{ColorFormat.YELLOW}Please close your current form first.")

        except Exception as e:
            self.logger.error(f"Error in show_add_warp_form: {e}")
            player.send_message(f"{ColorFormat.RED}Error opening add warp form.")

    def show_edit_warp_form(self, player: Player, warp: Dict, index: int) -> None:
        """Show form to edit an existing warp"""
        try:
            dimension_map = ["overworld", "nether", "the_end"]
            current_dim_index = dimension_map.index(warp["dimension"]) if warp["dimension"] in dimension_map else 0

            form = ModalForm(title="Edit Warp")

            form.add_control(TextInput(
                label="Warp Name",
                placeholder="New name...",
                default_value=warp["name"]
            ))
            form.add_control(Dropdown(
                label="Dimension",
                options=["Overworld", "Nether", "End"],
                default_index=current_dim_index
            ))
            form.add_control(TextInput(
                label="Coordinates",
                placeholder="x y z",
                default_value=" ".join(str(c) for c in warp["coords"])
            ))

            def on_submit(p: Player, data: str):
                try:
                    response = json.loads(data)

                    new_name = response[0][:15].strip()
                    new_dim_index = response[1]
                    new_coords_str = response[2]

                    if not new_name:
                        p.send_message(f"{ColorFormat.RED}Warp name cannot be empty!")
                        return

                    new_coords = [int(x) for x in new_coords_str.split() if x.strip().lstrip('-').isdigit()]
                    if len(new_coords) != 3:
                        p.send_message(f"{ColorFormat.RED}Invalid coordinates! Use whole numbers like '100 64 200'")
                        return

                    warp_data = self.get_player_warp_data(p)
                    new_dimension = dimension_map[new_dim_index]

                    warp_data["customWarps"][index] = {
                        "name": new_name,
                        "coords": new_coords,
                        "dimension": new_dimension
                    }

                    self.warp_db[p.name] = {"customWarps": warp_data["customWarps"]}

                    # Apply cooldown if not Admin
                    if not self.has_tag(p, "Admin"):
                        self.cooldown_db[p.name] = {"lastEdit": int(time.time() * 1000)}

                    self.save_database()

                    display_dim = self.dimension_names.get(new_dimension, new_dimension)
                    coords_str = " ".join(str(c) for c in new_coords)
                    p.send_message(f"{ColorFormat.GREEN}Updated warp: {new_name} at {coords_str} in {display_dim}")
                    self.show_main_menu(p)
                except Exception as e:
                    self.logger.error(f"Error in edit warp form submission: {e}")
                    p.send_message(f"{ColorFormat.RED}Error updating warp: {str(e)}")

            form.on_submit = on_submit

            if not self.safe_send_form(player, form):
                player.send_message(f"{ColorFormat.YELLOW}Please close your current form first.")

        except Exception as e:
            self.logger.error(f"Error in show_edit_warp_form: {e}")
            player.send_message(f"{ColorFormat.RED}Error opening edit warp form.")

    def show_manage_cooldown_form(self, admin: Player) -> None:
        """Show form to manage player cooldowns (admin only)"""
        try:
            form = ModalForm(title="Manage Player Cooldowns")

            form.add_control(TextInput(
                label="Enter player name to reset cooldown",
                placeholder="Player name"
            ))

            def on_submit(p: Player, data: str):
                try:
                    response = json.loads(data)
                    target_player_name = response[0].strip()

                    if not target_player_name:
                        p.send_message(f"{ColorFormat.RED}Please enter a player name.")
                        return

                    if target_player_name in self.cooldown_db:
                        del self.cooldown_db[target_player_name]
                        self.save_database()

                    p.send_message(f"{ColorFormat.GREEN}Edit cooldown reset for player {target_player_name}.")
                except Exception as e:
                    self.logger.error(f"Error in cooldown form submission: {e}")
                    p.send_message(f"{ColorFormat.RED}Error resetting cooldown")

            form.on_submit = on_submit

            if not self.safe_send_form(admin, form):
                admin.send_message(f"{ColorFormat.YELLOW}Please close your current form first.")

        except Exception as e:
            self.logger.error(f"Error in show_manage_cooldown_form: {e}")
            admin.send_message(f"{ColorFormat.RED}Error opening cooldown management form.")

    def show_all_players_warps(self, player: Player) -> None:
        """Show all warps from all players (admin only)"""
        if not self.has_tag(player, "Owner") and not self.has_tag(player, "Admin"):
            return

        try:
            form = ActionForm(title="All Players' Warps")

            warp_count = 0
            for player_name, data in self.warp_db.items():
                custom_warps = data.get("customWarps", [])
                for warp in custom_warps:
                    display_dim = self.dimension_names.get(warp["dimension"], warp["dimension"])
                    coords_str = " ".join(str(c) for c in warp["coords"])
                    button_text = f"{player_name}'s {warp['name']}\n§8{coords_str} - {display_dim}"
                    form.add_button(button_text, "textures/ui/World",
                                    lambda p, w=warp: self.handle_teleport_to_warp(p, w))
                    warp_count += 1

            if warp_count == 0:
                form.content = "No warps available from any players."

            form.add_button("Back\n§8Return to main menu", "textures/ui/back_button",
                            lambda p: self.show_main_menu(p))

            if not self.safe_send_form(player, form):
                player.send_message(f"{ColorFormat.YELLOW}Please close your current form first.")

        except Exception as e:
            self.logger.error(f"Error in show_all_players_warps: {e}")
            player.send_message(f"{ColorFormat.RED}Error: {str(e)}")

    def show_manage_fixed_warps_menu(self, admin: Player) -> None:
        """Show menu to manage fixed server warps (admin only)"""
        if not self.has_tag(admin, "Owner") and not self.has_tag(admin, "Admin"):
            return

        try:
            form = ActionForm(title="Manage Fixed Warps")

            # Add button for each fixed warp
            for warp_key, warp_data in self.fixed_warps_db.items():
                display_dim = self.dimension_names.get(warp_data["dimension"], warp_data["dimension"])
                coords_str = " ".join(str(c) for c in warp_data["coords"])
                button_text = f"Edit: {warp_data['name']}\n§8{coords_str} - {display_dim}"
                form.add_button(button_text, "textures/ui/editIcon",
                                lambda p, key=warp_key: self.show_edit_fixed_warp_form(p, key))

            # Add new fixed warp button
            form.add_button("Add New Fixed Warp\n§8Create server warp", "textures/ui/color_plus",
                            lambda p: self.show_add_fixed_warp_form(p))

            # Back button
            form.add_button("Back\n§8Return to main menu", "textures/ui/back_button",
                            lambda p: self.show_main_menu(p))

            if not self.safe_send_form(admin, form):
                admin.send_message(f"{ColorFormat.YELLOW}Please close your current form first.")

        except Exception as e:
            self.logger.error(f"Error in show_manage_fixed_warps_menu: {e}")
            admin.send_message(f"{ColorFormat.RED}Error opening fixed warps menu.")

    def show_add_fixed_warp_form(self, admin: Player) -> None:
        """Show form to add a new fixed warp (admin only)"""
        try:
            form = ModalForm(title="Add New Fixed Warp")

            form.add_control(TextInput(
                label="Warp Key (internal identifier)",
                placeholder="e.g., pvp_arena"
            ))
            form.add_control(TextInput(
                label="Display Name",
                placeholder="e.g., PvP Arena"
            ))
            form.add_control(Dropdown(
                label="Dimension",
                options=["Overworld", "Nether", "End"],
                default_index=0
            ))
            form.add_control(TextInput(
                label="Coordinates",
                placeholder="x y z - or leave blank for current position"
            ))

            def on_submit(p: Player, data: str):
                try:
                    response = json.loads(data)

                    warp_key = response[0].strip().lower().replace(" ", "_")
                    display_name = response[1].strip()
                    dimension_index = response[2]
                    coords_str = response[3]

                    if not warp_key or not display_name:
                        p.send_message(f"{ColorFormat.RED}Please enter both key and name!")
                        return

                    if warp_key in self.fixed_warps_db:
                        p.send_message(f"{ColorFormat.RED}A fixed warp with key '{warp_key}' already exists!")
                        return

                    dimension_map = ["minecraft:overworld", "minecraft:nether", "minecraft:the_end"]

                    if coords_str.strip():
                        coords = [int(x) for x in coords_str.split() if x.strip().lstrip('-').isdigit()]
                        if len(coords) != 3:
                            p.send_message(f"{ColorFormat.RED}Invalid coordinates! Use whole numbers like '100 64 200'")
                            return
                    else:
                        loc = p.location
                        coords = [int(loc.x), int(loc.y), int(loc.z)]

                    dimension = dimension_map[dimension_index]

                    self.fixed_warps_db[warp_key] = {
                        "name": display_name,
                        "coords": coords,
                        "dimension": dimension
                    }

                    self.save_database()

                    display_dim = self.dimension_names.get(dimension, dimension)
                    coords_str = " ".join(str(c) for c in coords)
                    p.send_message(
                        f"{ColorFormat.GREEN}Added fixed warp: {display_name} at {coords_str} in {display_dim}")
                    self.show_manage_fixed_warps_menu(p)

                except Exception as e:
                    self.logger.error(f"Error in add fixed warp form submission: {e}")
                    p.send_message(f"{ColorFormat.RED}Error adding fixed warp: {str(e)}")

            form.on_submit = on_submit
            form.on_close = lambda p: self.show_manage_fixed_warps_menu(p)

            if not self.safe_send_form(admin, form):
                admin.send_message(f"{ColorFormat.YELLOW}Please close your current form first.")

        except Exception as e:
            self.logger.error(f"Error in show_add_fixed_warp_form: {e}")
            admin.send_message(f"{ColorFormat.RED}Error opening add fixed warp form.")

    def show_edit_fixed_warp_form(self, admin: Player, warp_key: str) -> None:
        """Show form to edit a fixed warp (admin only)"""
        try:
            warp = self.fixed_warps_db.get(warp_key)
            if not warp:
                admin.send_message(f"{ColorFormat.RED}Fixed warp not found!")
                return

            dimension_map = ["minecraft:overworld", "minecraft:nether", "minecraft:the_end"]
            current_dim_index = dimension_map.index(warp["dimension"]) if warp["dimension"] in dimension_map else 0

            form = ModalForm(title=f"Edit: {warp['name']}")

            form.add_control(TextInput(
                label="Display Name",
                placeholder="New name...",
                default_value=warp["name"]
            ))
            form.add_control(Dropdown(
                label="Dimension",
                options=["Overworld", "Nether", "End"],
                default_index=current_dim_index
            ))
            form.add_control(TextInput(
                label="Coordinates",
                placeholder="x y z",
                default_value=" ".join(str(c) for c in warp["coords"])
            ))

            def on_submit(p: Player, data: str):
                try:
                    response = json.loads(data)

                    new_name = response[0].strip()
                    new_dim_index = response[1]
                    new_coords_str = response[2]

                    if not new_name:
                        p.send_message(f"{ColorFormat.RED}Warp name cannot be empty!")
                        return

                    new_coords = [int(x) for x in new_coords_str.split() if x.strip().lstrip('-').isdigit()]
                    if len(new_coords) != 3:
                        p.send_message(f"{ColorFormat.RED}Invalid coordinates! Use whole numbers like '100 64 200'")
                        return

                    new_dimension = dimension_map[new_dim_index]

                    self.fixed_warps_db[warp_key] = {
                        "name": new_name,
                        "coords": new_coords,
                        "dimension": new_dimension
                    }

                    self.save_database()

                    display_dim = self.dimension_names.get(new_dimension, new_dimension)
                    coords_str = " ".join(str(c) for c in new_coords)
                    p.send_message(
                        f"{ColorFormat.GREEN}Updated fixed warp: {new_name} at {coords_str} in {display_dim}")
                    self.show_manage_fixed_warps_menu(p)

                except Exception as e:
                    self.logger.error(f"Error in edit fixed warp form submission: {e}")
                    p.send_message(f"{ColorFormat.RED}Error updating fixed warp: {str(e)}")

            # Add delete option
            form.add_control(TextInput(
                label="Type 'DELETE' to remove this warp",
                placeholder="Leave blank to keep"
            ))

            def on_submit_with_delete(p: Player, data: str):
                try:
                    response = json.loads(data)
                    delete_confirm = response[3].strip().upper()

                    if delete_confirm == "DELETE":
                        del self.fixed_warps_db[warp_key]
                        self.save_database()
                        p.send_message(f"{ColorFormat.RED}Deleted fixed warp: {warp['name']}")
                        self.show_manage_fixed_warps_menu(p)
                    else:
                        on_submit(p, data)

                except Exception as e:
                    self.logger.error(f"Error in edit fixed warp form submission: {e}")
                    p.send_message(f"{ColorFormat.RED}Error updating fixed warp: {str(e)}")

            form.on_submit = on_submit_with_delete
            form.on_close = lambda p: self.show_manage_fixed_warps_menu(p)

            if not self.safe_send_form(admin, form):
                admin.send_message(f"{ColorFormat.YELLOW}Please close your current form first.")

        except Exception as e:
            self.logger.error(f"Error in show_edit_fixed_warp_form: {e}")
            admin.send_message(f"{ColorFormat.RED}Error opening edit fixed warp form.")

    def teleport_to_location(self, player: Player, x: int, y: int, z: int, dimension_id: str) -> None:
        """Teleport player to specified coordinates and dimension"""
        try:
            # Map dimension IDs to proper dimension names
            dimension_map = {
                "overworld": "minecraft:overworld",
                "nether": "minecraft:nether",
                "the_end": "minecraft:the_end",
                "minecraft:overworld": "minecraft:overworld",
                "minecraft:nether": "minecraft:nether",
                "minecraft:the_end": "minecraft:the_end"
            }

            # Get the proper dimension name
            proper_dimension_id = dimension_map.get(dimension_id, dimension_id)

            # Simple approach: use player's current location as template and modify coordinates
            current_location = player.location

            # Check if we need to change dimensions
            player_dim_name = getattr(player.dimension, 'name', None) if hasattr(player, 'dimension') else None

            if player_dim_name == proper_dimension_id:
                # Same dimension - just create new location with same dimension
                dimension = player.dimension
                self.logger.info(f"Teleporting within same dimension: {player_dim_name}")
            else:
                # Different dimension - try to find the target dimension
                dimension = None
                level = self.server.level

                # Debug: log available methods on level (only once)
                if not hasattr(self, '_logged_level_methods'):
                    level_methods = [method for method in dir(level) if not method.startswith('_')]
                    self.logger.info(f"Available level methods: {level_methods}")
                    self._logged_level_methods = True

                # Try different methods to get the dimension
                for method_name in ['get_dimension', 'getDimension']:
                    if hasattr(level, method_name):
                        try:
                            method = getattr(level, method_name)
                            dimension = method(proper_dimension_id)
                            self.logger.info(f"Got dimension using {method_name}: {proper_dimension_id}")
                            break
                        except Exception as e:
                            self.logger.info(f"Failed to get dimension using {method_name}: {e}")
                            continue

                # If still no dimension, try to get all dimensions and find the right one
                if dimension is None:
                    for method_name in ['get_dimensions', 'getDimensions', 'dimensions']:
                        if hasattr(level, method_name):
                            try:
                                method = getattr(level, method_name)
                                dimensions = method() if callable(method) else method
                                for dim in dimensions:
                                    dim_name = getattr(dim, 'name', None)
                                    if dim_name == proper_dimension_id:
                                        dimension = dim
                                        self.logger.info(f"Found dimension in list: {dim_name}")
                                        break
                                if dimension:
                                    break
                            except Exception as e:
                                self.logger.debug(f"Failed to get dimensions using {method_name}: {e}")
                                continue

                # Last resort: use player's current dimension and warn
                if dimension is None:
                    dimension = player.dimension
                    self.logger.warning(f"Could not find dimension {proper_dimension_id}, using player's current dimension as fallback")
                    player.send_message(f"{ColorFormat.YELLOW}Warning: Could not change to {proper_dimension_id}, teleporting within current dimension.")

            # Create location with dimension as first parameter (add 0.5 to center player on block)
            location = Location(dimension, x + 0.5, y, z + 0.5)
            player.teleport(location)

            display_dim = self.dimension_names.get(proper_dimension_id, proper_dimension_id)
            player.send_message(f"{ColorFormat.GREEN}Teleported to {display_dim}!")

        except Exception as e:
            self.logger.error(f"Teleport failed: {e}")
            player.send_message(f"{ColorFormat.RED}Teleport failed! The location may be invalid or unsafe.")